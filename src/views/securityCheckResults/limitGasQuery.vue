<script setup lang="ts">
import XForm from '@af-mobile-client-vue3/components/data/XForm/index.vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import { ref } from 'vue'

// 访问配置名
const configName = ref('toolSecurity')
// 访问服务名
const serviceName = ref('af-revenue')

const formGroupAddConstruction = ref(null)
</script>

<template>
  <NormalDataLayout id="XFormGroupView" title="新增限购">
    <template #layout_content>
      <XForm
        ref="formGroupAddConstruction"
        mode="新增"
        :config-name="configName"
        :service-name="serviceName"
      />
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
.main {
  padding: var(--base-interval-1);

  :deep(.van-search) {
    padding: 0;
  }
}
</style>
