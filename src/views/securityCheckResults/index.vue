<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
// import { defineEmits, ref } from 'vue'
import { useRouter } from 'vue-router'

// 定义事件
// const emit = defineEmits(['deleteRow'])
// 访问路由
const router = useRouter()
// // 获取默认值
const idKey = ref('o_id')

// 简易crud表单测试
const configName = ref('SecuritytoolCRUD')
const serviceName = ref('af-revenue')

// 资源权限测试
// const configName = ref('crud_sources_test')
// const serviceName = ref('af-system')

// 实际业务测试
// const configName = ref('lngChargeAuditMobileCRUD')
// const serviceName = ref('af-gaslink')

// 跳转到详情页面
// function toDetail(item) {
//   router.push({
//     name: 'XCellDetailView',
//     params: { id: item[idKey.value] }, // 如果使用命名路由，推荐使用路由参数而不是直接构建 URL
//     query: {
//       operName: item[operNameKey.value],
//       method:item[methodKey.value],
//       requestMethod:item[requestMethodKey.value],
//       operatorType:item[operatorTypeKey.value],
//       operUrl:item[operUrlKey.value],
//       operIp:item[operIpKey.value],
//       costTime:item[costTimeKey.value],
//       operTime:item[operTimeKey.value],
//
//       title: item[titleKey.value],
//       businessType: item[businessTypeKey.value],
//       status:item[statusKey.value]
//     }
//   })
// }

// 跳转到表单——以表单组来渲染纯表单
function toDetail(item) {
  router.push({
    name: 'XFormGroupView',
    query: {
      id: item[idKey.value],
      // id: item.rr_id,
      // o_id: item.o_id,
    },
  })
}

// 新增功能
// function addOption(totalCount) {
//   router.push({
//     name: 'XFormView',
//     params: { id: totalCount, openid: totalCount },
//     query: {
//       configName: configName.value,
//       serviceName: serviceName.value,
//       mode: '新增',
//     },
//   })
// }

// 修改功能
// function updateRow(result) {
//   router.push({
//     name: 'XFormView',
//     params: { id: result.o_id, openid: result.o_id },
//     query: {
//       configName: configName.value,
//       serviceName: serviceName.value,
//       mode: '修改',
//     },
//   })
// }

// // 删除功能
// function deleteRow(result) {
//   emit('deleteRow', result.o_id)
// }
</script>

<template>
  <NormalDataLayout id="XCellListView" title="限购查询">
    <template #layout_content>
      <XCellList
        :config-name="configName"
        :service-name="serviceName"
        :fix-query-form="{ o_f_oper_name: 'edu_test' }"
        :id-key="idKey"
        @to-detail="toDetail"
      />
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
</style>
